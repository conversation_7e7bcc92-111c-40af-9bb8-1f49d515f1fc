Write a readme about how to set up GitHub and Google Auth for SuperBase. That's inclusive of actually going to those individual sites. So for Google you go to the Cloud Platform and you create a client ID underneath the project. Assume the reader of this is absolutely no development experience. Creating the client ID, getting the client ID, the client secret. Putting in the redirect URI. As well as any sort of authorized API end points. So from the server that's our config <PERSON><PERSON> which was the local host colon 54321 and then for GitHub it's going into GitHub logging in going to developer settings going to API I believe and then creating an OAuth connect and then pasting in those values. So just expand on all that. Thanks.