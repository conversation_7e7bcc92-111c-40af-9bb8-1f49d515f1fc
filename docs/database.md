# Working with the Database

# Getting Started for Beginners

## Project Setup
Clone the repo and install dependencies - it's just copying the code to your computer and downloading what it needs to run.
[Learn about Git & GitHub](https://docs.github.com/en/get-started)

## Node.js
Install Node.js - it's the engine that runs JavaScript on your computer instead of in a browser.
[Node.js docs](https://nodejs.org/en/docs/)

## pnpm
Install pnpm - it's like Node.js but way faster and handles packages better.
[pnpm docs](https://pnpm.io/docs)

## Docker/OrbStack
Install Docker or OrbStack - they create little containers on your computer to run the database and other services.
[See our Docker/OrbStack guide](./docker-setup.md)

## Supabase
Start Supabase locally with `pnpx supabase start` - it's your database and authentication system all in one package.
[See our Supabase guide](./supabase-local-setup.md)

## Environment Setup
Copy environment variables - these are secret settings your app needs to connect to services.
[Learn about .env files](https://nextjs.org/docs/basic-features/environment-variables)

## Development Server
Run the dev server with `pnpm run dev` and open http://localhost:3000 - this starts your app so you can see it in the browser.
[Next.js docs](https://nextjs.org/docs/getting-started)


This project uses Supabase with declarative database schemas for managing the database structure and migrations. This guide will help you understand how to work with the database effectively.

# Remote Supabase

## What is Remote Supabase?
Remote Supabase is your actual database in the cloud - it's where your real users and data will live when your app goes live.
[Supabase platform](https://supabase.com/docs/guides/platform)

## Getting Project Keys
Find your project URL and API keys in the Supabase dashboard - these are like the address and password for your database in the cloud.
[Project API settings](https://supabase.com/dashboard/project/_/settings/api)

## Environment Variables
Add your Supabase project details to `.env.local` - these tell your app how to connect to your cloud database instead of the local one.
[Environment setup guide](https://supabase.com/docs/guides/auth/server-side/nextjs#set-up-environment-variables)

## Database Migrations
Use `supabase db push` to send your local database changes to the cloud - this keeps your remote database in sync with your local development.
[Database migrations guide](https://supabase.com/docs/guides/cli/local-development#deploy-database-changes)


## Quick Reference {#quick-reference}

Here are the common database commands you'll need:

```bash
# Start the database
pnpx supabase start

# Restart the database
pnpx supabase stop && pnpx supabase start

# Reset the database (caution: this will clear all data)
pnpx supabase db reset --local

# Check database status
pnpx supabase status

# Generate TypeScript types from the database
pnpx supabase gen types typescript --local > supabase/types/database.types.ts

# Create a new migration
pnpx supabase db diff | pnpx supabase migration new latest

# Link to remote Supabase project
pnpx supabase link
```

## Database Structure {#database-structure}

Our database schemas are managed declaratively in the `supabase/schemas` directory. This means instead of writing migration steps manually, we declare the desired state of our database, and Supabase generates the necessary migrations.

### Schema Location
- All schema files are stored in `supabase/schemas/*.sql`
- Migrations are automatically generated in `supabase/migrations/`
- Database types are generated in `supabase/types/database.types.ts`

## Working with Schemas {#working-with-schemas}

### Creating a New Schema

1. Create a new SQL file in `supabase/schemas/` directory:
```sql
-- supabase/schemas/your_table.sql
create table "your_table" (
  "id" uuid not null default gen_random_uuid(),
  "created_at" timestamp with time zone default timezone('utc'::text, now()) not null,
  -- Add your columns here
  primary key (id)
);
```

2. Generate a migration:
```bash
pnpx supabase db diff | pnpx supabase migration new latest
```

### Updating an Existing Schema

1. Modify the relevant schema file in `supabase/schemas/`
2. Stop the database: `pnpx supabase stop`
3. Generate a new migration: `pnpx supabase db diff | pnpx supabase migration new latest`
4. Start the database: `pnpx supabase start`
5. Apply the migration: `pnpx supabase migration up`

## Local Development {#local-development}

### Configuration
The database configuration is managed in `supabase/config.toml`. Key settings include:

- Database port: 54322
- Studio port: 54323
- API port: 54321
- JWT settings
- Authentication settings
- Storage settings

### Starting the Local Environment

1. Start the Supabase stack:
```bash
pnpx supabase start
```

2. Access local services:
- Studio: http://localhost:54323
- API: http://localhost:54321
- Database: postgresql://postgres:postgres@localhost:54322/postgres

### Connecting to Your Database

In your application code, use the Supabase client:

```typescript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)
```

## Database Migrations {#database-migrations}

### Creating a New Migration

1. Make changes to your schema files in `supabase/schemas/`
2. Generate a migration:
```bash
pnpx supabase db diff | pnpx supabase migration new latest
```

### Applying Migrations

Migrations are automatically applied when:
- Starting the local environment (`pnpx supabase start`)
- Deploying to production (`pnpx supabase db push`)

To manually apply migrations:
```bash
pnpx supabase migration up
```

### Rolling Back Changes

During development, you can reset to a specific version:
```bash
pnpx supabase db reset --version <version>
```

## Type Safety {#type-safety}

The project automatically generates TypeScript types from your database schema:

```bash
pnpx supabase gen types typescript --local > supabase/types/database.types.ts
```

Use these types in your application code:

```typescript
import { Database } from '@/supabase/types/database.types'

type Todo = Database['public']['Tables']['todos']['Row']
```

## Deployment {#deployment}

To deploy database changes to production:

1. Link your project (if not already done):
```bash
pnpx supabase link
```

2. Push your changes:
```bash
pnpx supabase db push
```

## Best Practices {#best-practices}

1. Always work with declarative schemas in `supabase/schemas/`
2. Generate migrations for all schema changes
3. Keep schemas focused and modular
4. Use TypeScript types for type safety
5. Test migrations locally before deploying
6. Backup production data before major changes
7. Review generated migrations before applying them


# Data Fetching

## What is Data Fetching?
Data fetching is how your app gets information from the database - it's like asking Supabase "give me all the posts" or "find this user's profile".
[Data API docs](https://supabase.com/docs/guides/api)

## Server-Side Fetching
Server Components can fetch data directly on the server - this is faster and more secure because the requests never reach the user's browser.
[Server component data fetching](https://nextjs.org/docs/app/building-your-application/data-fetching/fetching-caching-and-revalidating)

## Client-Side Fetching
Client Components fetch data in the browser with hooks like `useEffect` - useful for data that changes frequently or depends on user interaction.
[Supabase client-side fetching](https://supabase.com/docs/reference/javascript/select)

## Data Mutations
Creating, updating or deleting data uses Server Actions - these are special functions that run on the server even when called from client components.
[Server Actions guide](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations)

# Authentication

## What is Authentication?
Authentication verifies who users are - it's how your app knows who's logged in and what they're allowed to see or do.
[Auth concepts](https://supabase.com/docs/guides/auth)

## How Auth Works in This Project
We use Supabase Auth with Next.js - when users sign in, their session is stored in cookies and automatically refreshed by middleware.
[Server-side auth guide](https://supabase.com/docs/guides/auth/server-side/nextjs)

## Protecting Pages
To make a page private, check if the user is logged in with `const { data: { user } } = await supabase.auth.getUser()` and redirect if not.
[Auth helpers](https://supabase.com/docs/guides/auth/server-side/nextjs#access-user-info-from-server-component)

## Sign In Methods
The app supports email/password, magic links (passwordless), and social login options - all handled by Supabase in the background.
[Auth providers](https://supabase.com/docs/guides/auth/social-login)

# Docker/OrbStack Setup

## Docker
Docker lets your computer run other tiny computers inside it - we need this for the database and backend services.
[Docker docs](https://docs.docker.com/get-started/)

## OrbStack (Mac only)
OrbStack is like Docker but way faster and easier on your Mac - it won't drain your battery or make your computer slow.
[OrbStack docs](https://docs.orbstack.dev/)

## Installation
Just download the installer for your system, run it, and now your computer can run containers - think of them as lightweight apps that run exactly the same on every computer.
[Docker Desktop download](https://www.docker.com/products/docker-desktop/) | [OrbStack download](https://orbstack.dev/download)


# Shadcn Components

## What are Shadcn Components?
Shadcn/UI gives you copy-pastable React components that look good out of the box - they're not a package you install, but code you own and can modify.
[Shadcn/UI docs](https://ui.shadcn.com/docs)

## Adding Components
Run `npx shadcn@latest add button` to add a component - this copies the component's code directly into your project so you can customize it.
[Component installation guide](https://ui.shadcn.com/docs/components/accordion#installation)

## Using Components
Import components like `import { Button } from "@/components/ui/button"` and use them in your JSX like `<Button>Click me</Button>`.
[Component usage guide](https://ui.shadcn.com/docs/components/accordion#usage)

## Customizing
Since the components live in your codebase, you can edit them directly - find them in `src/components/ui/` and change whatever you want.
[Theming guide](https://ui.shadcn.com/docs/theming)
