# Supabase Authentication Setup with Google and GitHub

Welcome! This guide will walk you through setting up authentication for your Supabase project using Google and GitHub. Don't worry if you're new to development – we'll explain everything step-by-step.

## What is Supabase?

Supabase is an open-source alternative to Firebase. It provides developers with a suite of tools to build applications quickly, including a database, authentication, storage, and more. Think of it as a ready-made backend for your app.

## Why Use Authentication?

Authentication is how you verify the identity of your users. It's crucial for protecting user data and controlling access to different parts of your application. By using familiar login options like Google and GitHub, you make it easier and more secure for people to sign up and log in.

## What This Guide Covers

This document will guide you through the process of configuring Google and GitHub as login options (OAuth providers) for your Supabase project. We'll cover:

1.  **Prerequisites:** What you need before you start.
2.  **Setting up Google Authentication:** Creating the necessary credentials in Google Cloud Platform.
3.  **Setting up GitHub Authentication:** Creating the necessary credentials in GitHub Developer Settings.
4.  **Configuring Supabase:** Adding your Google and GitHub credentials to your Supabase project.
5.  **Testing:** Making sure everything works correctly.

## Prerequisites

Before we dive into the setup, make sure you have the following ready:

1.  **A Supabase Account and Project:**
    *   You need an account with Supabase and have already created a project. Think of a project as the container for your app's backend (database, authentication, etc.).
    *   If you don't have one, sign up at [supabase.com](https://supabase.com) and create a new project.

2.  **A Google Cloud Platform Account:**
    *   To allow users to log in with Google, you need an account with Google Cloud Platform (GCP).
    *   If you don't have one, you can create it at [cloud.google.com](https://cloud.google.com).

3.  **A GitHub Account:**
    *   Similarly, for GitHub login, you need a GitHub account.
    *   If you don't have one, sign up at [github.com](https://github.com).

**Important Concepts (Simplified):**

Don't worry if these terms sound technical. Here's a simple way to think about them:

*   **OAuth (Open Authorization):** Imagine you're checking into a hotel (your app). Instead of the hotel creating a new key card for you, they let you use your government ID (like your Google or GitHub account) to prove who you are. OAuth is the standard process that lets services like Google and GitHub securely confirm your identity to other apps (like Supabase) without sharing your password.
*   **Client ID:** Like a public username for your application when it talks to Google or GitHub.
*   **Client Secret:** Like a private password for your application. Keep this secret!
*   **Redirect URI (or Callback URL):** After you successfully log in with Google or GitHub, they need to know where to send you back to. This URL is that address within your application.

This guide assumes you have already created these accounts. We will focus on getting the specific keys and settings needed from Google Cloud and GitHub to connect them to your Supabase project.

Let's get started!

## Setting up Google Authentication

Now, let's get the Google side of things set up. We need to tell Google about your application so it can trust it when users try to log in with their Google accounts.

**Step 1: Go to Google Cloud Platform (GCP)**

*   Open your web browser and navigate to the [Google Cloud Console](https://console.cloud.google.com/).
*   Log in with the Google account you want to use for this project.
*   If this is your first time using GCP, you might need to agree to the terms of service and potentially set up a billing account (Google often provides a free tier or trial credits, but be aware of their policies).

**Step 2: Create a New Project (or Select an Existing One)**

*   Google Cloud organizes everything into "Projects". You'll need a project to manage the settings for your application's authentication.
*   Near the top of the console, you should see a project selection dropdown (it might say "Select a project" or show the name of an existing project).
*   Click the dropdown and select "NEW PROJECT".
    *   *(Screenshot Placeholder: Image showing the project selection dropdown and "NEW PROJECT" button)*
*   Give your project a descriptive name (e.g., "My Supabase App Auth"). The Project ID will be automatically generated, which is fine.
*   Choose an Organization and Location if prompted (usually optional for simple setups).
*   Click "CREATE".
    *   *(Screenshot Placeholder: Image showing the New Project creation form)*
*   Wait a moment for the project to be created. Make sure your new project is selected in the dropdown at the top.

**Step 3: Navigate to APIs & Services**

*   Once your project is selected, find the navigation menu (usually three horizontal lines ☰ in the top-left corner).
*   Click the menu and hover over "APIs & Services".
*   In the submenu that appears, click on "Credentials".
    *   *(Screenshot Placeholder: Image showing the navigation menu path: APIs & Services -> Credentials)*

This "Credentials" page is where we will configure the specific details (like the Client ID and Client Secret) in the next task. For now, you've successfully set up your GCP project and navigated to the right place!

**Step 4: Configure the OAuth Consent Screen**

*   Before creating credentials, Google needs to know what your users will see when asked to approve your app.
*   Click on "CONFIGURE CONSENT SCREEN".
    *   *(Screenshot Placeholder: Image showing the "CONFIGURE CONSENT SCREEN" button)*
*   Choose the "External" user type (unless you have a specific Google Workspace setup) and click "CREATE".
    *   *(Screenshot Placeholder: Image showing User Type selection)*
*   **App Information:**
    *   **App name:** Enter the name of your application (e.g., "My Awesome App"). This is what users will see.
    *   **User support email:** Select your email address.
    *   **App logo:** Optional, but recommended for a better user experience.
*   **Developer contact information:** Enter your email address.
*   Click "SAVE AND CONTINUE".
*   **Scopes:** You can leave this section as default for now. Click "SAVE AND CONTINUE".
*   **Test users:** While testing, you might want to add specific Google accounts that can use this login. For now, you can skip this. Click "SAVE AND CONTINUE".
*   **Summary:** Review the details and click "BACK TO DASHBOARD".
*   On the OAuth consent screen page, you might see a button like "PUBLISH APP". Click this and confirm to make your consent screen active for external users (you might need further verification from Google later for a production app, but this is usually sufficient for development/testing).

**Step 5: Create OAuth Client ID Credentials**

*   Now, back on the main "Credentials" page (Navigation Menu ☰ -> APIs & Services -> Credentials).
*   Click on "+ CREATE CREDENTIALS" at the top.
*   Select "OAuth client ID" from the dropdown.
    *   *(Screenshot Placeholder: Image showing "+ CREATE CREDENTIALS" -> "OAuth client ID")*
*   **Application type:** Choose "Web application".
    *   *(Screenshot Placeholder: Image showing Application type selection)*
*   **Name:** Give it a name, like "Web App for Supabase".
*   **Authorized JavaScript origins:** You can leave this blank for now.
*   **Authorized redirect URIs:** This is **VERY IMPORTANT**. This tells Google where to send the user back *after* they successfully log in.
    *   Click "+ ADD URI".
    *   Enter the Supabase callback URL. For local development with the Supabase CLI, this is typically:
        ```
        http://localhost:54321/auth/v1/callback
        ```
    *   **Note:** If you are using a hosted Supabase project, you will find your specific callback URL in your Supabase project's Authentication settings under "URL Configuration". It will look something like `https://<your-project-ref>.supabase.co/auth/v1/callback`.
    *   *(Screenshot Placeholder: Image showing the Authorized redirect URIs field with the callback URL entered)*
*   Click "CREATE".

**Step 6: Retrieve and Securely Store Your Google Credentials**

*   Upon clicking "CREATE", a popup window will appear displaying your **Your Client ID** and **Your Client Secret**.
    *   *(Screenshot Placeholder: Image showing the Client ID and Client Secret popup)*
*   **Retrieve these values:** Google usually provides convenient copy buttons next to each value. Click these buttons to copy the Client ID and Client Secret accurately.
*   **Secure Storage is Crucial:**
    *   **IMMEDIATELY** paste these values into a **secure location**. Examples include:
        *   A password manager (recommended for long-term storage).
        *   A secure, temporary text file on your computer *only* for the duration of this setup process (delete it afterwards!).
    *   **NEVER:**
        *   Save them in your code directly.
        *   Commit them to version control (like Git).
        *   Share them publicly or via insecure channels (like email or chat).
*   **Why?** The **Client Secret** is like a password for your application. If someone else gets it, they could potentially impersonate your application.
*   You will need both the Client ID and Client Secret in a later step to configure Supabase.
*   Once you have securely copied both values, click "OK" on the popup.

You have now successfully created and secured the necessary Google OAuth credentials!

## Setting up GitHub Authentication

Next, let's configure GitHub to allow users to log in using their GitHub accounts. Similar to Google, we need to register your application with GitHub.

**Step 1: Go to GitHub and Log In**

*   Open your web browser and navigate to [github.com](https://github.com).
*   Log in using your existing GitHub account. If you don't have one, you'll need to sign up first (as mentioned in the Prerequisites).

**Step 2: Navigate to Settings**

*   Once logged in, click on your profile picture in the top-right corner of the page.
*   From the dropdown menu, select "Settings".
    *   *(Screenshot Placeholder: Image showing the profile dropdown and "Settings" option)*

**Step 3: Find Developer Settings**

*   On the Settings page, look for a navigation menu on the left-hand side.
*   Scroll down to the bottom of this menu and click on "Developer settings".
    *   *(Screenshot Placeholder: Image showing the left-hand settings menu with "Developer settings" highlighted)*

**Step 4: Go to OAuth Apps**

*   Within the Developer settings menu (again, usually on the left), click on "OAuth Apps".
    *   *(Screenshot Placeholder: Image showing the Developer settings menu with "OAuth Apps" selected)*

This "OAuth Apps" page is where we will create a new application registration in the next task to get your GitHub Client ID and Client Secret. You're now in the right place!

**Step 5: Register a New OAuth Application**

*   On the "OAuth Apps" page, click the "New OAuth App" button (usually green and located near the top right).
    *   *(Screenshot Placeholder: Image showing the "New OAuth App" button)*
*   You will see a form to register your application. Fill it out as follows:
    *   **Application name:** Enter a name for your application (e.g., "My Supabase Test App"). This is what users will see when they are asked to authorize.
    *   **Homepage URL:** Enter the main URL for your application. If you're just testing locally, you can often use a placeholder like `http://localhost:3000` or your Supabase project URL.
    *   **Application description:** (Optional) Add a brief description of your app.
    *   **Authorization callback URL:** This is **VERY IMPORTANT**, just like the redirect URI for Google. It tells GitHub where to send the user back after they authorize your application.
        *   Enter the Supabase callback URL. For local development with the Supabase CLI, this is typically:
            ```
            http://localhost:54321/auth/v1/callback
            ```
        *   **Note:** Again, if you are using a hosted Supabase project, find your specific callback URL in your Supabase project's Authentication settings under "URL Configuration". It will look like `https://<your-project-ref>.supabase.co/auth/v1/callback`.
    *   *(Screenshot Placeholder: Image showing the filled-out "Register a new OAuth application" form)*
*   Click the "Register application" button.

**Step 6: Retrieve and Securely Store Your GitHub Credentials**

*   After clicking "Register application", you'll be taken to the settings page for your new OAuth App.
*   **Retrieve Client ID:** The **Client ID** is displayed plainly on this page. Use the copy button next to it or select and copy the value.
    *   *(Screenshot Placeholder: Image showing the GitHub OAuth App page with Client ID highlighted)*
*   **Generate and Retrieve Client Secret:**
    *   You need to generate a Client Secret. Click the "Generate a new client secret" button.
        *   *(Screenshot Placeholder: Image showing the "Generate a new client secret" button)*
    *   **IMPORTANT:** GitHub will generate and display the Client Secret **only once**. You *must* copy it right now.
    *   Use the copy button next to the newly generated secret.
        *   *(Screenshot Placeholder: Image showing the generated Client Secret with the copy button)*
*   **Secure Storage is Crucial:**
    *   **IMMEDIATELY** paste both the **Client ID** and the newly generated **Client Secret** into a **secure location** (like the place you stored your Google credentials):
        *   A password manager (recommended).
        *   A secure, temporary text file (delete it after setup).
    *   **NEVER** save these in your code, commit them to Git, or share them insecurely.
*   **Why?** The Client Secret acts as the password for your application on GitHub.
*   You will need both the Client ID and Client Secret later to configure Supabase.

You have now successfully created and secured the necessary GitHub OAuth credentials!

## Configuring Supabase

Now that you have the Client ID and Client Secret from both Google and GitHub, it's time to tell Supabase about them.

**Step 1: Navigate to Your Supabase Project Dashboard**

*   Go to [supabase.com](https://supabase.com) and log in.
*   Select the project you want to configure.

**Step 2: Go to Authentication Settings**

*   In the left-hand sidebar of your project dashboard, find the icon that looks like a person or user profile (this is the Authentication section).
*   Click on it.
    *   *(Screenshot Placeholder: Image showing the Supabase dashboard sidebar with the Authentication icon highlighted)*

**Step 3: Go to Providers**

*   Within the Authentication section, find the "Providers" tab or sub-menu (it might also be called "Auth Providers").
*   Click on it.
    *   *(Screenshot Placeholder: Image showing the Authentication section with "Providers" selected)*

**Step 4: Configure Google Provider**

*   Find "Google" in the list of available providers.
*   Click on it or the toggle switch next to it to enable it.
*   You will see fields for "Client ID" and "Client Secret".
*   Carefully copy the **Google Client ID** you saved earlier and paste it into the "Client ID" field.
*   Carefully copy the **Google Client Secret** you saved earlier and paste it into the "Client Secret" (or "Secret") field.
    *   *(Screenshot Placeholder: Image showing the Google provider settings with Client ID and Secret fields)*
*   **Important:** Make sure the "Redirect URI (Callback URL)" shown here in Supabase matches exactly what you entered in the Google Cloud Console (e.g., `http://localhost:54321/auth/v1/callback` for local development).
*   Click "Save" or ensure the toggle stays enabled.

**Step 5: Configure GitHub Provider**

*   Find "GitHub" in the list of available providers.
*   Click on it or the toggle switch next to it to enable it.
*   You will see fields for "Client ID" and "Client Secret".
*   Carefully copy the **GitHub Client ID** you saved earlier and paste it into the "Client ID" field.
*   Carefully copy the **GitHub Client Secret** you saved earlier and paste it into the "Client Secret" (or "Secret") field.
    *   *(Screenshot Placeholder: Image showing the GitHub provider settings with Client ID and Secret fields)*
*   **Important:** Make sure the "Redirect URI (Callback URL)" shown here in Supabase matches exactly what you entered in the GitHub OAuth App settings (e.g., `http://localhost:54321/auth/v1/callback` for local development).
*   Click "Save" or ensure the toggle stays enabled.

**(Optional) Step 6: Check Site URL**

*   Still within the Authentication settings, often under a "Settings" or "URL Configuration" tab, there's a field for "Site URL".
*   For local development, this is usually `http://localhost:3000` (or whatever port your frontend runs on). For a deployed app, it's your production URL.
*   Ensure this is set correctly, as Supabase uses it for email links and other functions.
    *   *(Screenshot Placeholder: Image showing the Site URL setting in Supabase)*

That's it! You've successfully configured Supabase to use Google and GitHub for authentication.

## Testing and Troubleshooting

After setting everything up, it's crucial to test that the login flows work correctly and know how to fix common problems.

**Testing the Login Flow:**

The exact way to test depends on how your application (frontend) is built, but generally, you'll need to:

1.  **Implement Login Buttons:** Add buttons in your application that trigger the Supabase login function for Google and GitHub. Supabase provides client libraries (like `supabase-js`) to make this easy.
    *   Example using `supabase-js`:
        ```javascript
        async function signInWithGoogle() {
          const { error } = await supabase.auth.signInWithOAuth({
            provider: 'google',
          });
          if (error) console.error('Error signing in with Google:', error);
        }

        async function signInWithGitHub() {
          const { error } = await supabase.auth.signInWithOAuth({
            provider: 'github',
          });
          if (error) console.error('Error signing in with GitHub:', error);
        }
        ```
2.  **Click the Google Login Button:** This should redirect you to a Google consent screen where you choose your account.
    *   *(Screenshot Placeholder: Image of the Google Consent Screen)*
3.  **Authorize:** After choosing your account, you should be redirected back to your application (specifically, the Redirect URI/Callback URL you configured).
4.  **Check Supabase:** Log in to your Supabase project dashboard, go to Authentication -> Users. You should see a new user listed with the Google provider icon.
    *   *(Screenshot Placeholder: Image of Supabase Users list showing a new Google user)*
5.  **Repeat for GitHub:** Click the GitHub login button, authorize the app on GitHub, get redirected back, and check for the new user in the Supabase dashboard.
    *   *(Screenshot Placeholder: Image of the GitHub Authorization Screen)*
    *   *(Screenshot Placeholder: Image of Supabase Users list showing a new GitHub user)*

**Common Troubleshooting Issues:**

*   **Error: Redirect URI Mismatch / Invalid Redirect URI**
    *   **Problem:** The Callback URL in your Supabase settings, your Google Cloud Console settings, and your GitHub OAuth App settings *must* match exactly.
    *   **Solution:** Double-check the "Authorized redirect URIs" in Google (Step 5) and the "Authorization callback URL" in GitHub (Step 5). Ensure they are *identical* to the URL shown in your Supabase Authentication -> Providers settings for Google and GitHub respectively. Check for typos, http vs https, trailing slashes, or port numbers (especially for local development like `http://localhost:54321/auth/v1/callback`).
    *   *(Screenshot Placeholder: Error message showing redirect URI mismatch)*
*   **Error: Invalid Credentials / Client Secret Error**
    *   **Problem:** The Client ID or Client Secret you entered into Supabase doesn't match what Google or GitHub has for your application.
    *   **Solution:** Carefully copy the Client ID and Client Secret again from Google Cloud (Credentials page) and GitHub (OAuth App settings page - you might need to regenerate the secret if you didn't save it). Paste them into the corresponding fields in your Supabase Authentication -> Providers settings. Ensure there are no extra spaces.
*   **Login Works, But User Doesn't Appear in Supabase:**
    *   **Problem:** Could be a configuration issue or a slight delay.
    *   **Solution:** Refresh the Users page in Supabase. Double-check that the specific provider (Google/GitHub) is enabled in Supabase Auth settings. Check the Supabase project logs for any specific errors (Project Settings -> Logs).
*   **Google/GitHub Shows an Error Page Immediately:**
    *   **Problem:** This often points to an issue with the configuration on the provider's side (Google Cloud/GitHub).
    *   **Solution:** Review the OAuth Consent Screen settings in Google Cloud. Ensure the app is published (even if in testing mode). Review the GitHub OAuth App settings for any obvious errors.

**FAQ:**

*   **Q: Why isn't my Google login working?**
    *   A: Check the common issues above, especially Redirect URI mismatch and incorrect credentials. Ensure your Google Cloud Project has the OAuth Consent screen configured and potentially published.
*   **Q: What to do if GitHub authentication fails?**
    *   A: Verify the Redirect URI and credentials in both GitHub and Supabase. Make sure you generated and saved the Client Secret correctly, as GitHub only shows it once.
*   **Q: Can I use different Redirect URIs for development and production?**
    *   A: Yes! Both Google and GitHub allow you to add multiple valid Redirect URIs. You would add your local callback (e.g., `http://localhost:54321/auth/v1/callback`) and your production callback (e.g., `https://myapp.com/api/auth/callback`). Supabase handles figuring out which one to use based on the request.

Congratulations! You should now have a working authentication setup using Google and GitHub with Supabase. 