{"name": "vibe-code-turbo-kit", "private": true, "scripts": {"clean": "rm -rf node_modules .next pnpm-lock.yaml", "check-types": "tsc --noEmit --pretty", "db:status": "pnpx supabase status", "db:restart": "pnpm run supabase:stop && pnpm run supabase:start", "db:reset": "pnpx supabase db reset --local", "db:link": "pnpx supabase link", "db:lint": "pnpx supabase db lint", "db:types": "supabase gen types typescript --local > src/lib/supabase/database.types.ts", "db:migration": "pnpx supabase db diff | pnpx supabase migration new latest", "dev": "NODE_OPTIONS='--inspect' pnpm next dev", "build": "pnpm next build", "start": "pnpm next start", "format": "pnpx biome format --write .", "lint": "pnpx biome lint .", "lint:fix": "pnpx biome lint --write .", "knip": "knip", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd"}, "dependencies": {"@ai-sdk/google": "^1.2.8", "@anthropic-ai/sdk": "^0.39.0", "@google/generative-ai": "^0.24.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@tailwindcss/typography": "^0.5.16", "@types/react-syntax-highlighter": "^15.5.13", "@uidotdev/usehooks": "^2.4.1", "ai": "^4.3.2", "autoprefixer": "10.4.20", "boxen": "^7.1.1", "chalk": "^5.3.0", "class-variance-authority": "^0.7.1", "cli-table3": "^0.6.3", "clsx": "^2.1.1", "cmdk": "^1.1.1", "commander": "^11.1.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "embla-carousel-react": "^8.6.0", "figlet": "^1.7.0", "gradient-string": "^2.0.2", "input-otp": "^1.4.2", "lucide-react": "^0.487.0", "next": "15.2.4", "next-mdx-remote": "^5.0.0", "next-safe-action": "^7.10.4", "next-themes": "^0.4.6", "openai": "^4.86.1", "ora": "^7.0.1", "react": "19.0.0", "react-day-picker": "8.10.1", "react-dom": "19.0.0", "react-hook-form": "^7.55.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "resend": "^4.2.0", "server-only": "^0.0.1", "sonner": "^2.0.3", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^22.13.14", "@types/react": "19.0.12", "knip": "^5.47.0", "postcss": "8.4.49", "supabase": "^2.20.5", "tailwind-merge": "^2.5.2", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.2"}}