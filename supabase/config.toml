project_id = "project_id"

[api]
enabled = true
port = 54321
schemas = ["public", "storage"]
extra_search_path = ["public", "extensions"]
max_rows = 1000000

[auth]
site_url = "http://localhost:3001"
additional_redirect_urls = ["https://localhost:3001", "http://localhost:54321/auth/v1/callback"]
jwt_expiry = 36000

[db]
port = 54322

[studio]
port = 54323

[auth.external.google]
enabled = true
client_id = "env(GOOGLE_CLIENT_ID)"
secret = "env(GOOGLE_SECRET)"
redirect_uri = "http://localhost:54321/auth/v1/callback"

[auth.email]
double_confirm_changes = true
enable_confirmations = true
enable_signup = true

[analytics]
enabled = true
port = 54327
vector_port = 54328
backend = "postgres"