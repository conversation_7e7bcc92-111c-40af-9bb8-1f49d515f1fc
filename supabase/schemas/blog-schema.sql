-- Users table
CREATE TABLE "users" (
  "id" bigint primary key generated always as identity,
  "name" text NOT NULL,
  "email" text NOT NULL UNIQUE,
  "password" text NOT NULL,
  "created_at" timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  "updated_at" timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
) WITH (OIDS=FALSE);

ALTER TABLE "users" ENABLE ROW LEVEL SECURITY;

-- Profiles table
CREATE TABLE public.profiles (
  id uuid NOT NULL,
  display_name text NOT NULL,
  current_elo integer NOT NULL DEFAULT 1200,
  highest_elo integer NULL,
  total_battles integer NULL DEFAULT 0,
  wins integer NULL DEFAULT 0,
  losses integer NULL DEFAULT 0,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  CONSTRAINT profiles_pkey PRIMARY KEY (id),
  CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS profiles_id_idx ON public.profiles USING btree (id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS profiles_current_elo_idx ON public.profiles USING btree (current_elo DESC) TABLESPACE pg_default;

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Posts table
CREATE TABLE "posts" (
  "id" bigint primary key generated always as identity,
  "title" text NOT NULL,
  "content" text NOT NULL,
  "author_id" bigint NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "created_at" timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  "updated_at" timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
) WITH (OIDS=FALSE);

CREATE INDEX idx_posts_author_id ON "posts"("author_id");
ALTER TABLE "posts" ENABLE ROW LEVEL SECURITY;

-- Comments table
CREATE TABLE "comments" (
  "id" bigint primary key generated always as identity,
  "content" text NOT NULL,
  "post_id" bigint NOT NULL REFERENCES "posts"("id") ON DELETE CASCADE,
  "author_id" bigint NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "created_at" timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  "updated_at" timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
) WITH (OIDS=FALSE);

CREATE INDEX idx_comments_post_id ON "comments"("post_id");
CREATE INDEX idx_comments_author_id ON "comments"("author_id");
ALTER TABLE "comments" ENABLE ROW LEVEL SECURITY;

-- Function to Insert Profile on New User
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, display_name, created_at, updated_at)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'display_name', split_part(NEW.email, '@', 1)),
        NOW(),
        NOW()
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for New Auth Users
CREATE TRIGGER new_user_trigger
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION handle_new_user();
