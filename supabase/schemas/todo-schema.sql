-- Todos table
CREATE TABLE "todos" (
  "id" bigint primary key generated always as identity,
  "text" text NOT NULL,
  "completed" boolean NOT NULL DEFAULT false,
  "created_at" timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  "updated_at" timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
) WITH (OIDS=FALSE);

ALTER TABLE "todos" ENABLE ROW LEVEL SECURITY;

-- Function to Insert Default Todo on New User
CREATE OR REPLACE FUNCTION handle_new_user_todo()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.todos (text, completed, created_at, updated_at)
    VALUES (
        'Welcome to your Todo list!',
        false,
        NOW(),
        NOW()
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for New Auth Users to Create Default Todo
CREATE TRIGGER new_user_todo_trigger
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION handle_new_user_todo();
