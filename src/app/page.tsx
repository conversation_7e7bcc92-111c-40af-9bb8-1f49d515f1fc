import { SubscribeInput } from "@/components/subscribe-input"; // Add this import
import { But<PERSON> } from "@/components/ui/button";
import { Check<PERSON>ir<PERSON>, <PERSON> } from "lucide-react";
import Link from "next/link";

export default async function Home() {
  return (
    <div className="flex flex-col min-h-screen bg-background">
      <main className="flex-1 w-full mx-auto px-4 py-12 md:py-16 lg:py-20 flex flex-col gap-12 md:gap-16 items-center">
        {/* Hero Section */}
        <section className="text-center flex flex-col items-center w-full max-w-4xl">
          <h1
            className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-4 
                         bg-gradient-to-r from-purple-400 to-orange-400 bg-clip-text text-transparent"
          >
            <span className="inline-block mr-2">🔥</span> Vibe Code{" "}
            <i className="font-light">Turbo Kit</i>
          </h1>
          <Button asChild size="lg">
            <Link
              href="https://github.com/parkerrex/vibe-with-ai-turbokit"
              target="_blank"
              rel="noopener noreferrer"
            >
              Clone the Repo
            </Link>
          </Button>
        </section>
      </main>
    </div>
  );
}
