import { Icons } from "@/components/icons"; // Import custom icons
import { type Contributor, contributors } from "@/lib/data/contributors"; // Adjust path if necessary
import { Github } from "lucide-react"; // Assuming you use lucide-react for icons
import Image from "next/image";
import Link from "next/link";

export default function ContributorsPage() {
  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-background via-background to-purple-900/10">
      <main className="flex-1 w-full max-w-6xl mx-auto px-4 py-16 md:py-20 lg:py-24 flex flex-col gap-16 md:gap-20 items-center">
        {/* Title Section */}
        <section className="text-center flex flex-col items-center w-full">
          <h1
            className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-4
                         bg-gradient-to-r from-purple-500 to-orange-500 bg-clip-text text-transparent"
          >
            Meet the Contributors
          </h1>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            The awesome people building and maintaining this project.
          </p>
        </section>

        {/* Contributors Grid */}
        <section className="w-full">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {contributors.map((contributor: Contributor) => (
              <div
                key={contributor.name}
                className="bg-card/60 border border-white/10 rounded-xl p-6 flex flex-col items-center text-center shadow-lg
                           backdrop-blur-sm hover:shadow-purple-500/20 hover:scale-[1.02] hover:border-white/20
                           transition-all duration-300 ease-in-out group"
              >
                <div className="w-24 h-24 rounded-full overflow-hidden mb-5 relative border-2 border-purple-500/50 group-hover:border-purple-500 transition-colors duration-300">
                  <Image
                    src={contributor.mainImage}
                    alt={`Profile picture of ${contributor.name}`}
                    fill
                    className="object-cover rounded-full scale-105 group-hover:scale-100 transition-transform duration-300 ease-in-out"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" // Added sizes for optimization
                  />
                </div>
                <h2 className="text-xl font-semibold mb-3 text-card-foreground">
                  {contributor.name}
                </h2>
                <div className="flex gap-5 mt-auto pt-3">
                  {" "}
                  {/* Pushed to bottom */}
                  <Link
                    href={contributor.x}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary transition-colors duration-200"
                    aria-label={`${contributor.name}'s Twitter/X Profile`}
                  >
                    <Icons.twitter className="w-6 h-6" />{" "}
                    {/* Slightly larger icons */}
                  </Link>
                  <Link
                    href={contributor.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary transition-colors duration-200"
                    aria-label={`${contributor.name}'s GitHub Profile`}
                  >
                    <Github className="w-6 h-6" /> {/* Slightly larger icons */}
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </section>
      </main>
    </div>
  );
}
