import { ThemeProvider } from "next-themes";
import localFont from "next/font/local";
import Link from "next/link";
import "./globals.css";
import { fetchGithubStars } from "@/lib/fetch-github-stars"; // Import the fetch function
import type { Metadata } from "next";

export const metadata: Metadata = {
  metadataBase: new URL("http://localhost:3000"),
  title: "Vibe Code Turbo Kit",
  description:
    "A Turbo starter kit for rapid vibe-driven development and AI integration.",
  icons: {
    icon: "/favicon.ico",
  },
  openGraph: {
    title: "Vibe Code Turbo Kit",
    description:
      "A Turbo starter kit for rapid vibe-driven development and AI integration.",
    siteName: "Vibe Code Kit",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Vibe Code Turbo Kit",
    description:
      "A Turbo starter kit for rapid vibe-driven development and AI integration.",
  },
};

// Keep font setup
const satoshi = localFont({
  src: [
    {
      path: "../fonts/Satoshi-Regular.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "../fonts/Satoshi-Italic.woff2",
      weight: "400",
      style: "italic",
    },
    {
      path: "../fonts/Satoshi-Medium.woff2",
      weight: "500",
      style: "normal",
    },
    {
      path: "../fonts/Satoshi-MediumItalic.woff2",
      weight: "500",
      style: "italic",
    },
    {
      path: "../fonts/Satoshi-Bold.woff2",
      weight: "700",
      style: "normal",
    },
    {
      path: "../fonts/Satoshi-BoldItalic.woff2",
      weight: "700",
      style: "italic",
    },
    {
      path: "../fonts/Satoshi-Black.woff2",
      weight: "900",
      style: "normal",
    },
    {
      path: "../fonts/Satoshi-BlackItalic.woff2",
      weight: "900",
      style: "italic",
    },
  ],
  display: "swap",
  variable: "--font-satoshi",
});

export default async function RootLayout({
  // Make the component async
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const githubData = await fetchGithubStars(); // Fetch data here

  return (
    <html
      lang="en"
      className={`${satoshi.variable} font-sans`}
      suppressHydrationWarning
    >
      <body
        className="bg-background text-foreground tracking-tighter"
        data-gramm="false"
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <main className="min-h-screen flex flex-col items-center">
            <div className="flex-1 w-full flex flex-col gap-20 items-center">
              <nav className="w-full flex justify-center border-b border-b-foreground/10 h-16">
                <div className="w-full max-w-5xl flex justify-between items-center p-3 px-5 text-sm">
                  <div className="flex gap-5 items-center font-semibold">
                    <Link href={"/"} className="font-bold text-lg">
                      Vibe Code Turbo Kit
                    </Link>{" "}
                    <Link
                      href="/#features"
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      Features
                    </Link>
                    <Link
                      href="/docs"
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      Docs
                    </Link>{" "}
                    {/* Placeholder link */}
                    <Link
                      href="https://github.com/harivansh-afk/vibe-with-ai-turbokit"
                      target="_blank"
                      rel="noreferrer"
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      GitHub
                    </Link>{" "}
                  </div>
                </div>
              </nav>
              <div className="flex flex-col gap-20 max-w-5xl p-5">
                {children}
              </div>
            </div>
          </main>
        </ThemeProvider>
      </body>
    </html>
  );
}
