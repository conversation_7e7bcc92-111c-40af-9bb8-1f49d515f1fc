"use client";
import { cn } from "@/lib/cn";
import Link from "next/link";
import { SubscribeInput } from "./subscribe-input";

export default function Footer({ className }: { className?: string }) {
  return (
    <footer className={cn(className, "py-12 px-4 md:px-6 bg-background")}>
      <div className="w-full px-4 md:px-6 lg:px-8">
        <div className="flex flex-col md:flex-row justify-between items-start gap-8">
          <div className="">
            <div className="mt-2">
              <section className="w-full text-left max-w-md py-8">
                <h2 className="text-2xl font-semibold mb-4">
                  Don't Miss Crucial Updates!
                </h2>
                <SubscribeInput />
              </section>
            </div>
          </div>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-8 md:gap-12 w-full md:w-auto">
            <div>
              <h3 className="font-semibold mb-4">Pages</h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/#features"
                    className="text-gray-600 hover:text-black dark:text-gray-400 dark:hover:text-white"
                  >
                    Features
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Follow the Creators</h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="https://x.com/ParkerRex"
                    className="text-gray-600 hover:text-black dark:text-gray-400 dark:hover:text-white"
                  >
                    Parker Rex
                  </Link>
                </li>
                <li>
                  <Link
                    href="https://x.com/HarivanshRathi"
                    className="text-gray-600 hover:text-black dark:text-gray-400 dark:hover:text-white"
                  >
                    Harivansh Rathi
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
